<template>
  <div class="p-4 sm:p-6">
    <!-- Header with Toggle and Add Button -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-neutral-900">{{ showEvents ? 'Events' : 'Services' }}</h1>
        <p class="text-sm sm:text-base text-neutral-600 mt-1">{{ showEvents ? 'Manage your events and activities' : 'Manage your services' }}</p>
      </div>
      <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
        <div class="flex bg-neutral-100 rounded-lg p-1 w-full sm:w-auto">
          <button @click="showEvents = false" :class="toggleButtonClass(false)" class="flex-1 sm:flex-none">
            <Icon name="lucide:briefcase" class="w-4 h-4" />
            <span>Services</span>
          </button>
          <button @click="showEvents = true" :class="toggleButtonClass(true)" class="flex-1 sm:flex-none">
            <Icon name="lucide:calendar" class="w-4 h-4" />
            <span>Events</span>
          </button>
        </div>

        <Button @click="handleAddClick" class="inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 w-full sm:w-auto">
          <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
          {{ showEvents ? 'Add Event' : 'Add Service' }}
        </Button>
      </div>
    </div>

    <!-- Search -->
    <div class="mb-6">
      <div class="relative max-w-full sm:max-w-md">
        <Icon name="lucide:search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4 sm:w-5 sm:h-5" />
        <input v-model="searchQuery" type="text" :placeholder="`Search ${showEvents ? 'events' : 'services'}...`"
                class="w-full pl-10 pr-4 py-2 sm:py-3 bg-white border border-neutral-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm sm:text-base" />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
      <div v-for="i in 8" :key="i" class="bg-white rounded-lg border border-neutral-200 overflow-hidden animate-pulse">
        <div class="aspect-square bg-neutral-200"></div>
        <div class="p-4 space-y-3">
          <div class="h-4 bg-neutral-200 rounded w-3/4"></div>
          <div class="h-3 bg-neutral-200 rounded w-full"></div>
          <div class="h-3 bg-neutral-200 rounded w-2/3"></div>
          <div class="flex justify-between items-center">
            <div class="h-3 bg-neutral-200 rounded w-1/4"></div>
            <div class="h-4 bg-neutral-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Grid -->
    <div v-else-if="currentItems.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
      <ServiceCard v-if="!showEvents" v-for="service in currentItems as Service[]" :key="service.id" :service="service" @edit="editItem" @delete="handleDeleteClick" />
      <EventCard v-else v-for="event in currentItems as Event[]" :key="event.id" :event="event" @edit="editItem" @delete="handleDeleteClick" />
    </div>

    <!-- Empty States -->
    <div v-else class="text-center py-8 sm:py-12">
      <Icon :name="emptyStateIcon" class="w-12 h-12 sm:w-16 sm:h-16 text-neutral-400 mx-auto mb-4" />
      <h3 class="text-base sm:text-lg font-medium text-neutral-900 mb-2">{{ emptyStateTitle }}</h3>
      <p class="text-sm sm:text-base text-neutral-600">{{ emptyStateMessage }}</p>
    </div>

    <!-- Modals -->
    <ServiceModal
      v-if="!showEvents && (showAddModal || editingItem)"
      :service="editingItem as Service"
      @close="closeModal"
      @save="handleSave"
    />
    <EventModal
      v-if="showEvents && (showAddModal || editingItem)"
      :event="editingItem as Event"
      @close="closeModal"
      @save="handleSave"
    />

    <!-- Delete Confirmation Modal -->
    <DeleteConfirmationModal
      :show="showDeleteModal"
      :item-type="showEvents ? 'event' : 'service'"
      :item-name="(itemToDelete as Event)?.title || (itemToDelete as Service)?.name"
      :item-description="itemToDelete?.description"
      :loading="deleteLoading"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup lang="ts">
// Import types and components for services and events management
import type { Service, ServiceFormData, ServiceCategory, Event, EventFormData } from '@/types/service.types'
import { $apiFetch } from '~/composables/apiFetch'
import { getErrorMessage } from '~/utils/error'
import ServiceModal from '~/components/services/ServiceModal.vue'
import EventModal from '~/components/services/EventModal.vue'
import ServiceCard from '@/components/services/ServiceCard.vue'
import EventCard from '~/components/services/EventCard.vue'
import DeleteConfirmationModal from '~/components/ui/DeleteConfirmationModal.vue'
import { Button } from '@/components/ui/button'

definePageMeta({ layout: 'dashboard' })
useHead({
  title: 'Services & Events - Bookiime',
  meta: [{ name: 'description', content: 'Manage your services, events and offerings' }]
})

// State
const services = ref<Service[]>([])
const events = ref<Event[]>([])
const loading = ref(false)
const showEvents = ref(false)
const searchQuery = ref('')
const showAddModal = ref(false)
const editingItem = ref<Service | Event | null>(null)
const showDeleteModal = ref(false)
const itemToDelete = ref<Service | Event | null>(null)
const deleteLoading = ref(false)

// API Helper Functions - now using improved error utility
const handleApiError = (error: unknown, defaultMessage: string) => {
  console.error(error)
  return getErrorMessage(error)
}

// Filter Functions
const filterServices = (services: Service[], query: string) => {
  if (!query) return services
  const lowercaseQuery = query.toLowerCase()
  return services.filter(service =>
    service.name.toLowerCase().includes(lowercaseQuery) ||
    service.description.toLowerCase().includes(lowercaseQuery)
  )
}

const filterEvents = (events: Event[], query: string) => {
  if (!query) return events
  const lowercaseQuery = query.toLowerCase()
  return events.filter(event =>
    event.title.toLowerCase().includes(lowercaseQuery) ||
    event.description.toLowerCase().includes(lowercaseQuery)
  )
}

// Use useApiFetch at the top level
const { data: servicesData, refresh: refreshServices } = useApiFetch<Service[]>('/services')
const { data: eventsData, refresh: refreshEvents } = useApiFetch<Event[]>('/events/tenant')

// Service API Functions
const serviceApi = {
  async getAll(): Promise<Service[]> {
    try {
      await refreshServices()
      return servicesData.value || []
    } catch (error) {
      console.error('Error fetching all services:', error)
      throw error
    }
  },

  async getById(id: string): Promise<Service> {
    try {
      return await $apiFetch<Service>(`/services/${id}`, undefined, { method: 'GET' })
    } catch (error) {
      console.error('Error fetching service:', error)
      throw error
    }
  },

  async create(data: ServiceFormData, imageFile?: File): Promise<Service> {
    try {
      const formData = new FormData()
      formData.append('name', data.name)
      formData.append('price', data.price.toString())
      formData.append('duration', data.duration.toString())
      formData.append('isActive', data.isActive.toString())
      formData.append('allowOverlap', data.allowOverlap.toString())

      if (data.description) formData.append('description', data.description)
      if (data.categoryId) formData.append('categoryId', data.categoryId)
      if (imageFile) formData.append('image', imageFile)

      return await $apiFetch<Service>('/services', formData)
    } catch (error) {
      console.error('Error creating service:', error)
      throw error
    }
  },

  async update(id: string, data: Partial<ServiceFormData>, imageFile?: File): Promise<Service> {
    try {
      const formData = new FormData()

      // Only append defined values
      if (data.name !== undefined) formData.append('name', data.name)
      if (data.price !== undefined) formData.append('price', data.price.toString())
      if (data.duration !== undefined) formData.append('duration', data.duration.toString())
      if (data.description !== undefined) formData.append('description', data.description)
      if (data.categoryId !== undefined) formData.append('categoryId', data.categoryId)
      if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString())
      if (data.allowOverlap !== undefined) formData.append('allowOverlap', data.allowOverlap.toString())
      if (imageFile) formData.append('image', imageFile)

      return await $apiFetch<Service>(`/services/${id}`, formData, { method: 'PUT' })
    } catch (error) {
      console.error('Error updating service:', error)
      throw error
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await $apiFetch(`/services/${id}`, undefined, { method: 'DELETE' })
    } catch (error) {
      console.error('Error deleting service:', error)
      throw error
    }
  }
}

// Event API Functions
const eventApi = {
  async getAll(): Promise<Event[]> {
    try {
      await refreshEvents()
      return eventsData.value || []
    } catch (error) {
      console.error('Error fetching all events:', error)
      throw error
    }
  },

  async getTenant(): Promise<Event[]> {
    try {
      await refreshEvents()
      return eventsData.value || []
    } catch (error) {
      console.error('Error fetching tenant events:', error)
      throw error
    }
  },

  async getById(id: string): Promise<Event> {
    try {
      return await $apiFetch<Event>(`/events/${id}`, undefined, { method: 'GET' })
    } catch (error) {
      console.error('Error fetching event:', error)
      throw error
    }
  },

  async create(data: EventFormData, imageFile?: File): Promise<Event> {
    try {
      const formData = new FormData()
      formData.append('title', data.title)
      formData.append('description', data.description)
      formData.append('startDate', data.startDate)
      formData.append('endDate', data.endDate)
      formData.append('startTime', data.startTime)
      formData.append('endTime', data.endTime)
      formData.append('isActive', data.isActive.toString())

      if (data.location) formData.append('location', data.location)
      if (data.category) formData.append('category', data.category)
      if (data.maxAttendees !== undefined) formData.append('maxAttendees', data.maxAttendees.toString())
      if (data.price !== undefined) formData.append('price', data.price.toString())
      if (imageFile) formData.append('image', imageFile)

      return await $apiFetch<Event>('/events', formData)
    } catch (error) {
      console.error('Error creating event:', error)
      throw error
    }
  },

  async update(id: string, data: Partial<EventFormData>, imageFile?: File): Promise<Event> {
    try {
      const formData = new FormData()

      // Only append defined values
      if (data.title !== undefined) formData.append('title', data.title)
      if (data.description !== undefined) formData.append('description', data.description)
      if (data.startDate !== undefined) formData.append('startDate', data.startDate)
      if (data.endDate !== undefined) formData.append('endDate', data.endDate)
      if (data.startTime !== undefined) formData.append('startTime', data.startTime)
      if (data.endTime !== undefined) formData.append('endTime', data.endTime)
      if (data.location !== undefined) formData.append('location', data.location)
      if (data.category !== undefined) formData.append('category', data.category)
      if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString())
      if (data.maxAttendees !== undefined) formData.append('maxAttendees', data.maxAttendees.toString())
      if (data.price !== undefined) formData.append('price', data.price.toString())
      if (imageFile) formData.append('image', imageFile)

      return await $apiFetch<Event>(`/events/${id}`, formData, { method: 'PUT' })
    } catch (error) {
      console.error('Error updating event:', error)
      throw error
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await $apiFetch(`/events/${id}`, undefined, { method: 'DELETE' })
    } catch (error) {
      console.error('Error deleting event:', error)
      throw error
    }
  }
}

// Category API Functions
const categoryApi = {
  async getAll(): Promise<ServiceCategory[]> {
    try {
      const { data } = await useApiFetch<ServiceCategory[]>('/categories')
      return data.value || []
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error
    }
  },

  async create(data: { name: string; description?: string }): Promise<ServiceCategory> {
    try {
      return await $apiFetch<ServiceCategory>('/categories', data)
    } catch (error) {
      console.error('Error creating category:', error)
      throw error
    }
  },

  async update(id: string, data: { name?: string; description?: string }): Promise<ServiceCategory> {
    try {
      return await $apiFetch<ServiceCategory>(`/categories/${id}`, data, { method: 'PUT' })
    } catch (error) {
      console.error('Error updating category:', error)
      throw error
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await $apiFetch(`/categories/${id}`, undefined, { method: 'DELETE' })
    } catch (error) {
      console.error('Error deleting category:', error)
      throw error
    }
  }
}

// Computed properties
const currentItems = computed(() => showEvents.value ? filterEvents(events.value, searchQuery.value) : filterServices(services.value, searchQuery.value))
const emptyStateIcon = computed(() => showEvents.value ? 'lucide:calendar' : 'lucide:package')
const emptyStateTitle = computed(() => currentItems.value.length === 0 && searchQuery.value ? `No ${showEvents.value ? 'events' : 'services'} match your search` : `No ${showEvents.value ? 'events' : 'services'} found`)
const emptyStateMessage = computed(() => currentItems.value.length === 0 && searchQuery.value ? 'Try adjusting your search terms.' : `Get started by adding your first ${showEvents.value ? 'event' : 'service'}.`)

// Methods
const toggleButtonClass = (isEvents: boolean) => [
  'flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
  showEvents.value === isEvents ? 'bg-white text-primary-600 shadow-sm' : 'text-neutral-600 hover:text-neutral-900'
]

const loadData = async () => {
  loading.value = true
  try {
    console.log('Loading services and events data...')
    console.log('API Base URL:', useRuntimeConfig().public.apiBase)

    const [servicesResult, eventsResult] = await Promise.all([
      serviceApi.getAll(),
      eventApi.getTenant()
    ])
    console.log('Services data:', servicesResult)
    console.log('Events data:', eventsResult)
    services.value = servicesResult
    events.value = eventsResult
  } catch (err) {
    console.error('Error loading data:', err)
    console.error('Error details:', err)
    $toast(handleApiError(err, 'Failed to load data'), { type: 'error' })
  } finally {
    loading.value = false
  }
}

const editItem = (item: Service | Event) => {
  editingItem.value = { ...item }
}

const handleDeleteClick = (item: Service | Event) => {
  itemToDelete.value = item
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!itemToDelete.value) return

  deleteLoading.value = true
  try {
    if (showEvents.value) {
      await eventApi.delete(itemToDelete.value.id)
      events.value = events.value.filter((e: Event) => e.id !== itemToDelete.value!.id)
    } else {
      await serviceApi.delete(itemToDelete.value.id)
      services.value = services.value.filter((s: Service) => s.id !== itemToDelete.value!.id)
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} deleted successfully`, { type: 'success' })
    cancelDelete()
  } catch (err) {
    $toast(handleApiError(err, `Failed to delete ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  } finally {
    deleteLoading.value = false
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  itemToDelete.value = null
  deleteLoading.value = false
}

const handleSave = async (data: ServiceFormData | EventFormData, imageFile?: File | null) => {
  try {
    if (showEvents.value) {
      const eventData = data as EventFormData
      if (editingItem.value) {
        const updated = await eventApi.update(editingItem.value.id, eventData, imageFile || undefined)
        const index = events.value.findIndex((e: Event) => e.id === editingItem.value!.id)
        if (index !== -1) events.value[index] = updated
      } else {
        const newEvent = await eventApi.create(eventData, imageFile || undefined)
        events.value.push(newEvent)
      }
    } else {
      const serviceData = data as ServiceFormData
      if (editingItem.value) {
        const updated = await serviceApi.update(editingItem.value.id, serviceData, imageFile || undefined)
        const index = services.value.findIndex((s: Service) => s.id === editingItem.value!.id)
        if (index !== -1) services.value[index] = updated
      } else {
        const newService = await serviceApi.create(serviceData, imageFile || undefined)
        services.value.push(newService)
      }
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} ${editingItem.value ? 'updated' : 'created'} successfully`, { type: 'success' })
    closeModal()
  } catch (err) {
    $toast(handleApiError(err, `Failed to save ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  }
}

const handleAddClick = () => {
  console.log('Add button clicked, showEvents:', showEvents.value)
  showAddModal.value = true
  console.log('showAddModal set to:', showAddModal.value)
}

const closeModal = () => {
  showAddModal.value = false
  editingItem.value = null
}

onMounted(() => {
  console.log('Services page mounted')
  console.log('Auth state:', useNuxtApp().$auth.value)
  loadData()
})

</script>