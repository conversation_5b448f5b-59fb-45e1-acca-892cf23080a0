<template>
  <NuxtLayout name="dashboard">
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-h4 font-bold text-neutral-900">Bookings</h1>

        <!-- Create Dialog Trigger -->
        <Dialog v-model:open="isCreateOpen">
          <DialogTrigger as-child>
            <button
              class="px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white shadow-md transition"
            >
              + Create Booking
            </button>
          </DialogTrigger>

          <!-- DialogContent: centered & scrollable when tall -->
          <DialogContent
            class="w-full sm:max-w-[720px] max-h-[80vh] overflow-y-auto"
          >
            <DialogHeader>
              <DialogTitle>Create Booking</DialogTitle>
              <DialogDescription
                >Fill in the details to create a new booking.</DialogDescription
              >
            </DialogHeader>

            <form @submit.prevent="createBooking" class="space-y-4 mt-4 pb-6">
              <InputField
                v-model="form.serviceId"
                label="Service"
                placeholder="Select service"
              />
              <!-- InputSelect for services -->
              <InputSelect
                v-model="form.serviceId"
                :options="serviceOptions"
                label="Service"
                placeholder="Choose service"
              />

              <InputField
                v-model="form.customerEmail"
                label="Customer Email"
                type="email"
                required
                placeholder="<EMAIL>"
              />
              <InputField
                v-model="form.customerName"
                label="Customer Name"
                required
                placeholder="Full name"
              />
              <InputField
                v-model="form.customerPhone"
                label="Customer Phone"
                placeholder="Phone number"
              />

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    class="block text-body-3 font-medium text-neutral-700 mb-2"
                    >Start At</label
                  >
                  <input
                    type="datetime-local"
                    v-model="startAtDateString"
                    class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label
                    class="block text-body-3 font-medium text-neutral-700 mb-2"
                    >End At</label
                  >
                  <input
                    type="datetime-local"
                    v-model="endAtDateString"
                    class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <InputField
                  v-model="bufferBeforeStr"
                  label="Buffer Before (mins)"
                  type="number"
                  :required="false"
                />
                <InputField
                  v-model="bufferAfterStr"
                  label="Buffer After (mins)"
                  type="number"
                  :required="false"
                />
              </div>

              <InputTextarea v-model="form.notes" label="Notes" />

              <div>
                <label
                  class="block text-body-3 font-medium text-neutral-700 mb-2"
                  >Booking Image</label
                >
                <input
                  type="file"
                  @change="handleFile"
                  class="w-full mt-2 border border-neutral-300 rounded-lg p-2"
                />
                <div
                  v-if="form.bookingImageName"
                  class="text-sm text-neutral-600 mt-2"
                >
                  {{ form.bookingImageName }}
                </div>
              </div>

              <div class="flex justify-end gap-3 mt-2">
                <DialogClose as-child>
                  <button
                    type="button"
                    class="px-4 py-2 bg-neutral-200 rounded-lg"
                  >
                    Cancel
                  </button>
                </DialogClose>
                <button
                  type="submit"
                  class="px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white shadow-md"
                  :disabled="loading"
                >
                  {{ loading ? "Creating..." : "Create Booking" }}
                </button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

     <DataTable 
      :columns="columns" 
      :data="bookings" 
    />

      <!-- VIEW DIALOG -->
      <Dialog v-model:open="isViewOpen">
        <DialogContent
          class="w-full sm:max-w-[600px] max-h-[80vh] overflow-y-auto"
        >
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
          </DialogHeader>

          <div class="space-y-3 mt-3">
            <div>
              <strong>Customer:</strong> {{ selectedBooking?.customerName }}
            </div>
            <div>
              <strong>Email:</strong> {{ selectedBooking?.customerEmail }}
            </div>
            <div>
              <strong>Phone:</strong>
              {{ selectedBooking?.customerPhone || "-" }}
            </div>
            <div>
              <strong>Service:</strong>
              {{ serviceName(selectedBooking?.serviceId) }}
            </div>
            <div>
              <strong>Start:</strong> {{ formatDate(selectedBooking?.startAt) }}
            </div>
            <div>
              <strong>End:</strong> {{ formatDate(selectedBooking?.endAt) }}
            </div>
            <div>
              <strong>Notes:</strong>
              <div class="mt-1 text-neutral-700">
                {{ selectedBooking?.notes || "-" }}
              </div>
            </div>
          </div>

          <div class="mt-4 flex justify-end">
            <DialogClose as-child>
              <button class="px-4 py-2 bg-neutral-200 rounded-lg">Close</button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      <!-- EDIT DIALOG -->
      <Dialog v-model:open="isEditOpen">
        <DialogContent
          class="w-full sm:max-w-[720px] max-h-[80vh] overflow-y-auto"
        >
          <DialogHeader>
            <DialogTitle>Edit Booking</DialogTitle>
          </DialogHeader>

          <form @submit.prevent="updateBooking" class="space-y-4 mt-4 pb-6">
            <InputSelect
              v-model="editForm.serviceId"
              :options="serviceOptions"
              label="Service"
            />
            <InputField
              v-model="editForm.customerEmail"
              label="Customer Email"
              type="email"
            />
            <InputField v-model="editForm.customerName" label="Customer Name" />
            <InputField
              v-model="editForm.customerPhone"
              label="Customer Phone"
            />
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  class="block text-body-3 font-medium text-neutral-700 mb-2"
                  >Start At</label
                >
                <input
                  type="datetime-local"
                  v-model="editStartAtDateString"
                  class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label
                  class="block text-body-3 font-medium text-neutral-700 mb-2"
                  >End At</label
                >
                <input
                  type="datetime-local"
                  v-model="editEndAtDateString"
                  class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <InputField
                v-model="editBufferBeforeStr"
                label="Buffer Before (mins)"
                type="number"
              />
              <InputField
                v-model="editBufferAfterStr"
                label="Buffer After (mins)"
                type="number"
              />
            </div>
            <InputTextarea v-model="editForm.notes" label="Notes" />
            <div>
              <label class="block text-body-3 font-medium text-neutral-700 mb-2"
                >Booking Image</label
              >
              <input
                type="file"
                @change="handleEditFile"
                class="w-full mt-2 border border-neutral-300 rounded-lg p-2"
              />
              <div
                v-if="editForm.bookingImageName"
                class="text-sm text-neutral-600 mt-2"
              >
                {{ editForm.bookingImageName }}
              </div>
            </div>

            <div class="flex justify-end gap-3">
              <DialogClose as-child>
                <button
                  type="button"
                  class="px-4 py-2 bg-neutral-200 rounded-lg"
                >
                  Cancel
                </button>
              </DialogClose>
              <button
                type="submit"
                class="px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white shadow-md"
                :disabled="editLoading"
              >
                {{ editLoading ? "Saving..." : "Save" }}
              </button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <!-- DELETE CONFIRM -->
      <Dialog v-model:open="isDeleteOpen">
        <DialogContent class="w-full sm:max-w-[420px]">
          <DialogHeader>
            <DialogTitle>Delete Booking</DialogTitle>
            <DialogDescription
              >Are you sure you want to delete this booking?</DialogDescription
            >
          </DialogHeader>

          <div class="mt-4 flex justify-end gap-3">
            <DialogClose as-child>
              <button class="px-4 py-2 bg-neutral-200 rounded-lg">
                Cancel
              </button>
            </DialogClose>
            <button
              @click="confirmDelete"
              class="px-4 py-2 rounded-lg bg-red-600 text-white"
              :disabled="deleteLoading"
            >
              {{ deleteLoading ? "Deleting..." : "Delete" }}
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button'
import type { ColumnDef } from '@tanstack/vue-table'
import type { Booking } from "~/types/booking.types";
import {h} from 'vue'

// Define columns
const columns: ColumnDef<Booking>[] = [
  {
    accessorKey: 'customerName',
    header: 'Customer',
  },
  {
    accessorKey: 'customerEmail',
    header: 'Email',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'service',
    header: 'Service',
  },
  {
    accessorKey: 'startAt',
    header: 'Start',
    cell: ({ row }) => formatDate(row.original.startAt),
  },
  {
    accessorKey: 'endAt',
    header: 'End',
    cell: ({ row }) => formatDate(row.original.endAt),
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const booking = row.original
      return h('div', { class: 'flex gap-2' }, [
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openView(booking),
        }, () => 'View'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openEdit(booking),
        }, () => 'Edit'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          class: 'text-red-600',
          onClick: () => openDelete(booking),
        }, () => 'Delete'),
      ])   
    },
  },
]

// Fetch data
const {
  data: bookingsResp,
  pending,
  refresh,
} = useApiFetch<any[]>("/bookings");
const { data: servicesResp } = useApiFetch<any[]>("/services");

const bookings = computed(() => bookingsResp?.value ?? []);
const servicesList = computed(() => servicesResp?.value ?? []);

const serviceOptions = computed(() =>
  servicesList.value.map((s) => ({
    id: s.id,
    label: s.name || s.title || s.serviceName || s.id,
  }))
);

// helper: map serviceId to name
function serviceName(id?: string) {
  if (!id) return "-";
  const s = servicesList.value.find((x) => x.id === id);
  return s ? s.name || s.title || s.serviceName || s.id : id;
}

const isCreateOpen = ref(false);
const isViewOpen = ref(false);
const isEditOpen = ref(false);
const isDeleteOpen = ref(false);

const loading = ref(false);
const editLoading = ref(false);
const deleteLoading = ref(false);

const selectedBooking = ref<any | null>(null);

// FORM (create)
const form = reactive({
  serviceId: "",
  customerEmail: "",
  customerName: "",
  customerPhone: "",
  bufferBefore: null as number | null,
  bufferAfter: null as number | null,
  notes: "",
  bookingImage: null as File | null,
  bookingImageName: "",
});

const startAtDate = ref<Date | null>(null);
const endAtDate = ref<Date | null>(null);

// Computed properties for datetime-local inputs
const startAtDateString = computed({
  get: () => startAtDate.value ? formatDateTimeLocal(startAtDate.value) : "",
  set: (value: string) => startAtDate.value = value ? new Date(value) : null
});

const endAtDateString = computed({
  get: () => endAtDate.value ? formatDateTimeLocal(endAtDate.value) : "",
  set: (value: string) => endAtDate.value = value ? new Date(value) : null
});

const bufferBeforeStr = computed({
  get: () => (form.bufferBefore != null ? String(form.bufferBefore) : ""),
  set: (v: string) => (form.bufferBefore = v ? Number(v) : null),
});
const bufferAfterStr = computed({
  get: () => (form.bufferAfter != null ? String(form.bufferAfter) : ""),
  set: (v: string) => (form.bufferAfter = v ? Number(v) : null),
});

function handleFile(e: Event) {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    form.bookingImage = input.files[0];
    form.bookingImageName = input.files[0].name;
  }
}

async function createBooking() {
  if (!form.serviceId) {
    return alert("Please select a service");
  }

  loading.value = true;
  try {
    const fd = new FormData();
    fd.append("serviceId", form.serviceId);
    fd.append("customerEmail", form.customerEmail);
    fd.append("customerName", form.customerName);
    if (form.customerPhone) fd.append("customerPhone", form.customerPhone);
    if (startAtDate.value)
      fd.append("startAt", startAtDate.value.toISOString());
    if (endAtDate.value) fd.append("endAt", endAtDate.value.toISOString());
    if (form.bufferBefore != null)
      fd.append("bufferBefore", String(form.bufferBefore));
    if (form.bufferAfter != null)
      fd.append("bufferAfter", String(form.bufferAfter));
    if (form.notes) fd.append("notes", form.notes);
    if (form.bookingImage) fd.append("bookingImage", form.bookingImage);

    await $apiFetch("/bookings", fd);

    await refresh();
    resetForm();
    isCreateOpen.value = false;
  } catch (err) {
    console.error(err);
    alert("Failed creating booking");
  } finally {
    loading.value = false;
  }
}

function resetForm() {
  form.serviceId = "";
  form.customerEmail = "";
  form.customerName = "";
  form.customerPhone = "";
  form.bufferBefore = null;
  form.bufferAfter = null;
  form.notes = "";
  form.bookingImage = null;
  form.bookingImageName = "";
  startAtDate.value = null;
  endAtDate.value = null;
}

// VIEW
function openView(b: any) {
  selectedBooking.value = b;
  isViewOpen.value = true;
}

// EDIT
const editForm = reactive({
  id: "",
  serviceId: "",
  customerEmail: "",
  customerName: "",
  customerPhone: "",
  bufferBefore: null as number | null,
  bufferAfter: null as number | null,
  notes: "",
  bookingImage: null as File | null,
  bookingImageName: "",
});
const editStartAtDate = ref<Date | null>(null);
const editEndAtDate = ref<Date | null>(null);

// Computed properties for edit datetime-local inputs
const editStartAtDateString = computed({
  get: () => editStartAtDate.value ? formatDateTimeLocal(editStartAtDate.value) : "",
  set: (value: string) => editStartAtDate.value = value ? new Date(value) : null
});

const editEndAtDateString = computed({
  get: () => editEndAtDate.value ? formatDateTimeLocal(editEndAtDate.value) : "",
  set: (value: string) => editEndAtDate.value = value ? new Date(value) : null
});
const editBufferBeforeStr = computed({
  get: () =>
    editForm.bufferBefore != null ? String(editForm.bufferBefore) : "",
  set: (v: string) => (editForm.bufferBefore = v ? Number(v) : null),
});
const editBufferAfterStr = computed({
  get: () => (editForm.bufferAfter != null ? String(editForm.bufferAfter) : ""),
  set: (v: string) => (editForm.bufferAfter = v ? Number(v) : null),
});

function openEdit(b: any) {
  selectedBooking.value = b;
  editForm.id = b.id;
  editForm.serviceId = b.serviceId;
  editForm.customerEmail = b.customerEmail;
  editForm.customerName = b.customerName;
  editForm.customerPhone = b.customerPhone;
  editForm.bufferBefore = b.bufferBefore ?? null;
  editForm.bufferAfter = b.bufferAfter ?? null;
  editForm.notes = b.notes ?? "";
  editForm.bookingImage = null;
  editForm.bookingImageName = "";
  editStartAtDate.value = b.startAt ? new Date(b.startAt) : null;
  editEndAtDate.value = b.endAt ? new Date(b.endAt) : null;
  isEditOpen.value = true;
}

function handleEditFile(e: Event) {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    editForm.bookingImage = input.files[0];
    editForm.bookingImageName = input.files[0].name;
  }
}

async function updateBooking() {
  editLoading.value = true;
  try {
    const fd = new FormData();
    fd.append("serviceId", editForm.serviceId);
    fd.append("customerEmail", editForm.customerEmail);
    fd.append("customerName", editForm.customerName);
    if (editForm.customerPhone)
      fd.append("customerPhone", editForm.customerPhone);
    if (editStartAtDate.value)
      fd.append("startAt", editStartAtDate.value.toISOString());
    if (editEndAtDate.value)
      fd.append("endAt", editEndAtDate.value.toISOString());
    if (editForm.bufferBefore != null)
      fd.append("bufferBefore", String(editForm.bufferBefore));
    if (editForm.bufferAfter != null)
      fd.append("bufferAfter", String(editForm.bufferAfter));
    if (editForm.notes) fd.append("notes", editForm.notes);
    if (editForm.bookingImage) fd.append("bookingImage", editForm.bookingImage);

    await $apiFetch(`/bookings/${editForm.id}`, fd, {
      method: "PUT",
    });

    await refresh();
    isEditOpen.value = false;
  } catch (err) {
    console.error(err);
    alert("Failed updating booking");
  } finally {
    editLoading.value = false;
  }
}

// DELETE
function openDelete(b: any) {
  selectedBooking.value = b;
  isDeleteOpen.value = true;
}

async function confirmDelete() {
  if (!selectedBooking.value) return;
  deleteLoading.value = true;
  try {
    await $apiFetch(`/bookings/${selectedBooking.value.id}`, undefined, {
      method: "DELETE",
    });
    await refresh();
    isDeleteOpen.value = false;
  } catch (err) {
    console.error(err);
    alert("Delete failed");
  } finally {
    deleteLoading.value = false;
  }
}

// helpers
function formatDate(s?: string) {
  if (!s) return "-";
  try {
    return new Date(s).toLocaleString();
  } catch {
    return s;
  }
}

function formatDateTimeLocal(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}
</script>
